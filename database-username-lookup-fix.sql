
-- Fix for username lookup during authentication
-- Create a secure function to look up email by username

-- Create a function that can look up email by username
CREATE OR REPLACE FUNCTION public.lookup_email_by_username(lookup_username TEXT)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER -- This runs with the privileges of the function creator
AS $$
DECLARE
  user_email TEXT;
BEGIN
  -- Clean the input
  lookup_username := LOWER(TRIM(lookup_username));
  
  -- Look up the email
  SELECT email INTO user_email
  FROM public.profiles
  WHERE LOWER(username) = lookup_username
  LIMIT 1;
  
  RETURN user_email;
END;
$$;

-- Grant execute permission to anonymous and authenticated users
GRANT EXECUTE ON FUNCTION public.lookup_email_by_username(TEXT) TO anon;
GRANT EXECUTE ON FUNCTION public.lookup_email_by_username(TEXT) TO authenticated;

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'Username lookup function created successfully!';
  RAISE NOTICE 'Use: SELECT lookup_email_by_username(''username'') to get email';
END $$;
