
# CaptionCraft - Multi-Platform Caption Repurposer

Transform one post into four platform-optimized captions for Twitter/X, LinkedIn, Instagram, and TikTok.

## Features

- ✨ **AI-Powered Generation**: Uses advanced LLM to create platform-specific captions
- 🎯 **Platform Optimized**: Follows 2025 best practices for each social platform
- ⚡ **Real-time Streaming**: See captions generate in real-time
- 📊 **Character Counting**: Visual feedback on optimal character limits
- 💾 **Caption History**: Save and manage your generated captions
- 🌙 **Dark Theme**: Beautiful dark interface optimized for daily use
- 📱 **Responsive Design**: Works perfectly on all devices

## Platform Specifications

- **Twitter/X**: 71-100 chars optimal, 2 hashtags max, conversational tone
- **LinkedIn**: 150-300 chars optimal, 5 hashtags max, professional tone  
- **Instagram**: 100-150 chars optimal, 15 hashtags max, creative tone
- **TikTok**: 80-100 chars optimal, 5 hashtags max, trend-focused tone

## Pricing

- **Free Trial**: 2 caption generations included
- **Pro Plan**: $20/month for unlimited generations

## Quick Start

### 1. Database Setup

Run this SQL in your Supabase SQL Editor:

\`\`\`sql
-- Copy and paste the content from database-setup.sql
\`\`\`

### 2. Environment Variables

The following environment variables are already configured:

\`\`\`
ABACUSAI_API_KEY="your-api-key"
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
\`\`\`

### 3. Install and Run

\`\`\`bash
cd app
yarn install
yarn dev
\`\`\`

Visit http://localhost:3000

## Tech Stack

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS + shadcn/ui components
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **AI**: Abacus.ai LLM API with streaming
- **Deployment**: Ready for Vercel/Netlify

## Project Structure

\`\`\`
app/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   │   ├── generate/      # Caption generation endpoint
│   │   ├── captions/      # Caption management
│   │   └── user/         # User statistics
│   ├── dashboard/         # User dashboard page
│   ├── globals.css        # Dark theme styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx          # Landing page
├── components/            # React components
│   ├── auth/             # Authentication components
│   ├── caption/          # Caption generation UI
│   ├── layout/           # Layout components
│   └── ui/               # shadcn/ui components
├── lib/                  # Utility libraries
│   ├── auth.ts           # Authentication helpers
│   ├── caption-generator.ts  # Core generation logic
│   ├── supabase.ts       # Database client
│   └── usage-tracker.ts  # Usage analytics
└── hooks/                # Custom React hooks
    └── use-caption-generator.ts
\`\`\`

## Key Features Implementation

### 🤖 AI-Powered Caption Generation
- Streams responses for real-time feedback
- Platform-specific prompt engineering
- Error handling and retry logic

### 📊 Usage Tracking & Limits
- Free trial: 2 generations per user
- Subscription tracking
- Usage analytics dashboard

### 🎨 Dark Theme UI
- Carefully crafted dark color scheme
- High contrast ratios for accessibility
- Smooth animations and transitions

### 🔒 Authentication & Security
- Supabase Row Level Security (RLS)
- Secure API endpoints
- User session management

## Sample Content

The app includes sample content for testing:

1. **Business/SaaS**: Pricing psychology insights
2. **Health/Wellness**: Morning routine optimization  
3. **Technology/AI**: AI writing tools analysis

## Database Schema

### Core Tables
- `profiles`: User information and subscription status
- `caption_generations`: Generated caption sets
- `usage_logs`: Usage tracking and analytics

### Security
- Row Level Security enabled
- User isolation policies
- Automatic profile creation triggers

## API Endpoints

- `POST /api/generate` - Generate captions with streaming
- `GET /api/captions` - Retrieve caption history
- `POST /api/captions` - Save caption to history
- `DELETE /api/captions` - Remove caption
- `GET /api/user/stats` - User statistics and usage

## Performance Features

- Server-side streaming for real-time updates
- Optimistic UI updates
- Efficient database queries with indexes
- Image optimization and lazy loading

## Deployment Notes

1. Set up Supabase project and run database schema
2. Configure environment variables in deployment platform
3. Deploy to Vercel, Netlify, or similar platform
4. Test authentication and API endpoints

## Support

- Built following 2025 social media best practices
- Responsive design for all devices
- Crash prevention with null-safe coding
- Error boundaries and graceful fallbacks

---

**Ready to transform your content strategy? Start your free trial today!**
