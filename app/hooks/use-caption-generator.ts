
'use client'

import { useState, useCallback } from 'react'
import { CaptionSet } from '@/lib/caption-generator'

export interface GenerationState {
  isGenerating: boolean
  progress: number
  message: string
  results: CaptionSet | null
  error: string | null
  usageInfo: {
    trialUsesRemaining: number
    subscriptionStatus: string
  } | null
}

export function useCaptionGenerator() {
  const [state, setState] = useState<GenerationState>({
    isGenerating: false,
    progress: 0,
    message: '',
    results: null,
    error: null,
    usageInfo: null
  })

  const generateCaptions = useCallback(async (
    originalContent: string,
    contentType: string,
    targetTone: string,
    userId: string
  ) => {
    setState(prev => ({
      ...prev,
      isGenerating: true,
      progress: 0,
      message: 'Starting generation...',
      results: null,
      error: null
    }))

    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          originalContent,
          contentType,
          targetTone,
          userId
        })
      })

      if (!response?.ok) {
        const errorData = await response?.json?.()
        throw new Error(errorData?.error || 'Failed to generate captions')
      }

      const reader = response?.body?.getReader?.()
      const decoder = new TextDecoder()
      let buffer = ''

      while (reader) {
        const { done, value } = await reader?.read?.() ?? { done: true, value: undefined }
        if (done) break

        buffer += decoder?.decode?.(value, { stream: true }) ?? ''
        let lines = buffer?.split?.('\n') ?? []
        buffer = lines?.pop?.() ?? ''

        for (const line of lines) {
          if (line?.startsWith?.('data: ')) {
            const data = line?.slice?.(6)
            if (data === '[DONE]') {
              return
            }
            
            try {
              const parsed = JSON?.parse?.(data)
              
              if (parsed?.status === 'processing') {
                setState(prev => ({
                  ...prev,
                  progress: parsed?.progress ?? prev?.progress ?? 0,
                  message: parsed?.message ?? prev?.message ?? ''
                }))
              } else if (parsed?.status === 'completed') {
                setState(prev => ({
                  ...prev,
                  isGenerating: false,
                  progress: 100,
                  message: 'Captions generated successfully!',
                  results: parsed?.data,
                  usageInfo: parsed?.data?.usageInfo
                }))
                return
              } else if (parsed?.status === 'error') {
                throw new Error(parsed?.message || 'Generation failed')
              }
            } catch (parseError) {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isGenerating: false,
        error: error instanceof Error ? error?.message : 'An error occurred',
        progress: 0,
        message: ''
      }))
    }
  }, [])

  const reset = useCallback(() => {
    setState({
      isGenerating: false,
      progress: 0,
      message: '',
      results: null,
      error: null,
      usageInfo: null
    })
  }, [])

  return {
    ...state,
    generateCaptions,
    reset
  }
}
