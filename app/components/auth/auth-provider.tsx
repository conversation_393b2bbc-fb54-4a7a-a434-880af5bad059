
'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { supabaseAdmin } from '@/lib/supabase-admin'

interface UserProfile {
  id: string
  email: string
  username?: string
  full_name?: string
  subscription_status?: string
  subscription_plan?: string
  trial_uses_remaining?: number
  created_at?: string
}

interface AuthUser extends User {
  profile?: UserProfile
}

interface AuthContextType {
  user: AuthUser | null
  session: Session | null
  loading: boolean
  profile: UserProfile | null
  signIn: (email: string, password: string) => Promise<{ error?: AuthError | null }>
  signUp: (email: string, password: string, metadata?: any) => Promise<{ error?: AuthError | null }>
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  loading: true,
  profile: null,
  signIn: async () => ({ error: null }),
  signUp: async () => ({ error: null }),
  signOut: async () => {},
})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchUserProfile = async (userId: string) => {
    console.log('👤 AuthProvider: Fetching profile for user:', userId)
    
    try {
      console.log('🔍 AuthProvider: Starting Supabase query with ADMIN client...')
      
      // Add timeout to prevent hanging
      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        console.log('⏰ AuthProvider: Profile fetch timeout after 5 seconds')
        controller.abort()
      }, 5000)

      // Use admin client to bypass RLS issues
      const { data, error } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      clearTimeout(timeoutId)

      console.log('📊 AuthProvider: Supabase admin query response:', { 
        hasData: !!data, 
        hasError: !!error,
        errorCode: error?.code,
        errorMessage: error?.message
      })

      if (error) {
        console.error('❌ AuthProvider: Error fetching profile with admin client:', {
          error,
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        })
        return null
      }

      console.log('✅ AuthProvider: Profile fetched successfully with admin client:', { 
        username: data?.username, 
        email: data?.email,
        id: data?.id,
        fullData: data
      })
      return data
    } catch (error) {
      console.error('❌ AuthProvider: Exception fetching profile:', error)
      if ((error as Error)?.name === 'AbortError') {
        console.log('🚫 AuthProvider: Profile fetch was aborted due to timeout')
      }
      return null
    }
  }

  const updateUserWithProfile = async (authUser: User | null) => {
    console.log('🔄 AuthProvider: Updating user with profile:', {
      hasAuthUser: !!authUser,
      authUserEmail: authUser?.email,
      authUserId: authUser?.id
    })
    
    if (!authUser) {
      console.log('🚪 AuthProvider: No auth user, clearing state')
      setUser(null)
      setProfile(null)
      return
    }

    console.log('🔍 AuthProvider: Starting profile fetch...')
    
    // Fetch user profile with timeout fallback
    let userProfile = null
    try {
      userProfile = await fetchUserProfile(authUser.id)
      console.log('📋 AuthProvider: Profile fetch completed:', { 
        success: !!userProfile,
        username: userProfile?.username 
      })
    } catch (error) {
      console.error('💥 AuthProvider: Profile fetch failed completely:', error)
    }

    // Always create the user object, even if profile fetch failed
    const extendedUser: AuthUser = {
      ...authUser,
      profile: userProfile
    }

    console.log('💾 AuthProvider: Setting user state:', {
      hasExtendedUser: !!extendedUser,
      hasProfile: !!userProfile,
      profileUsername: userProfile?.username,
      userEmail: authUser.email
    })
    
    setUser(extendedUser)
    setProfile(userProfile)
  }

  // Authentication methods
  const signIn = async (email: string, password: string) => {
    console.log('🔑 AuthProvider: Signing in user:', email)
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error('❌ AuthProvider: Sign in error:', error)
        return { error }
      }

      console.log('✅ AuthProvider: Sign in successful')
      return { error: null }
    } catch (error) {
      console.error('💥 AuthProvider: Sign in exception:', error)
      return { error: error as AuthError }
    }
  }

  const signUp = async (email: string, password: string, metadata?: any) => {
    console.log('📝 AuthProvider: Signing up user:', email)
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata || {}
        }
      })

      if (error) {
        console.error('❌ AuthProvider: Sign up error:', error)
        return { error }
      }

      console.log('✅ AuthProvider: Sign up successful')
      return { error: null }
    } catch (error) {
      console.error('💥 AuthProvider: Sign up exception:', error)
      return { error: error as AuthError }
    }
  }

  const signOut = async () => {
    console.log('🚪 AuthProvider: Signing out user')
    try {
      const { error } = await supabase.auth.signOut()
      if (error) {
        console.error('❌ AuthProvider: Sign out error:', error)
      }
      console.log('✅ AuthProvider: Sign out successful')
    } catch (error) {
      console.error('💥 AuthProvider: Sign out exception:', error)
    }
  }

  useEffect(() => {
    console.log('🚀 AuthProvider: Initializing...')
    let mounted = true

    // Get initial session
    const getInitialSession = async () => {
      console.log('📍 AuthProvider: Getting initial session')
      try {
        const { data: { session: initialSession }, error } = await supabase.auth.getSession()
        
        console.log('📄 AuthProvider: Initial session result:', {
          hasSession: !!initialSession,
          hasUser: !!initialSession?.user,
          userEmail: initialSession?.user?.email,
          error: error?.message
        })
        
        if (error) {
          console.error('❌ AuthProvider: Error getting session:', error)
        }
        
        if (mounted) {
          setSession(initialSession)
          
          console.log('🔄 AuthProvider: Starting profile update...')
          
          try {
            await updateUserWithProfile(initialSession?.user || null)
            console.log('✅ AuthProvider: Initial setup complete')
            setLoading(false)
          } catch (updateError) {
            console.error('💥 AuthProvider: Profile update failed:', updateError)
            setLoading(false)
          }
        }
      } catch (error) {
        console.error('💥 AuthProvider: Session initialization error:', error)
        if (mounted) {
          console.log('⚠️ AuthProvider: Setting loading to false due to error')
          setLoading(false)
        }
      }
    }

    getInitialSession()

    // Listen for auth changes
    console.log('👂 AuthProvider: Setting up auth state change listener')
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔔 AuthProvider: Auth state changed:', {
        event,
        hasSession: !!session,
        hasUser: !!session?.user,
        userEmail: session?.user?.email,
        mounted
      })
      
      if (mounted) {
        console.log('🔄 AuthProvider: Processing auth state change')
        setSession(session)
        
        try {
          await updateUserWithProfile(session?.user || null)
          console.log('✅ AuthProvider: Auth state change complete')
          setLoading(false)
        } catch (error) {
          console.error('💥 AuthProvider: Auth state change profile update failed:', error)
          setLoading(false)
        }
      }
    })

    return () => {
      console.log('🧹 AuthProvider: Cleanup - unmounting')
      mounted = false
      subscription?.unsubscribe()
    }
  }, [])

  const value = {
    user,
    session,
    loading,
    profile,
    signIn,
    signUp,
    signOut,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
