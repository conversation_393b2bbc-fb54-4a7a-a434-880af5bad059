
'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { PlatformPreview } from './platform-preview'
import { useCaptionGenerator } from '@/hooks/use-caption-generator'
import { useAuth } from '@/components/auth/auth-provider'
import { RefreshCw, Save, CheckCircle } from 'lucide-react'

export function CaptionResults() {
  const { user } = useAuth()
  const { results, isGenerating, error, usageInfo } = useCaptionGenerator()
  const [saved, setSaved] = useState(false)
  const [saving, setSaving] = useState(false)

  const handleSave = async () => {
    if (!results?.id || !user?.id) return

    setSaving(true)
    try {
      const response = await fetch('/api/captions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          captionId: results?.id,
          userId: user?.id
        })
      })

      if (response?.ok) {
        setSaved(true)
        setTimeout(() => setSaved(false), 3000)
      } else {
        console.error('Failed to save captions')
      }
    } catch (error) {
      console.error('Error saving captions:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleRegenerate = () => {
    // This would trigger regeneration - implementation depends on parent component
    console.log('Regenerate requested')
  }

  if (error) {
    return (
      <Card className="w-full max-w-4xl mx-auto border-red-200 bg-red-50 dark:bg-red-950">
        <CardHeader>
          <CardTitle className="text-red-700 dark:text-red-300">Generation Failed</CardTitle>
          <CardDescription className="text-red-600 dark:text-red-400">
            {error}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            variant="outline" 
            onClick={handleRegenerate}
            className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (!results && !isGenerating) {
    return null
  }

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Generated Captions</CardTitle>
            <CardDescription>
              Platform-optimized captions ready for your social media posts
              {usageInfo && (
                <span className="ml-2 text-sm">
                  • {usageInfo?.trialUsesRemaining} trial uses remaining
                </span>
              )}
            </CardDescription>
          </div>
          
          {results && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleRegenerate}
                disabled={isGenerating}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isGenerating ? 'animate-spin' : ''}`} />
                Regenerate
              </Button>
              
              <Button
                onClick={handleSave}
                disabled={saving || saved}
                className={saved ? 'bg-green-600 hover:bg-green-700' : ''}
              >
                {saved ? (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Saved!
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {saving ? 'Saving...' : 'Save to History'}
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <PlatformPreview
            platform="twitter"
            caption={results?.twitter?.caption ?? ''}
            charCount={results?.twitter?.charCount ?? 0}
            captionId={results?.id}
          />
          
          <PlatformPreview
            platform="linkedin"
            caption={results?.linkedin?.caption ?? ''}
            charCount={results?.linkedin?.charCount ?? 0}
            captionId={results?.id}
          />
          
          <PlatformPreview
            platform="instagram"
            caption={results?.instagram?.caption ?? ''}
            charCount={results?.instagram?.charCount ?? 0}
            captionId={results?.id}
          />
          
          <PlatformPreview
            platform="tiktok"
            caption={results?.tiktok?.caption ?? ''}
            charCount={results?.tiktok?.charCount ?? 0}
            captionId={results?.id}
          />
        </div>

        {results && (
          <div className="mt-4 text-sm text-muted-foreground text-center">
            Generated in {((results?.generationTime ?? 0) / 1000)?.toFixed?.(1)}s
          </div>
        )}
      </CardContent>
    </Card>
  )
}
