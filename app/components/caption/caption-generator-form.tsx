
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useCaptionGenerator } from '@/hooks/use-caption-generator'
import { useAuth } from '@/components/auth/auth-provider'
import { Sparkles, Loader2 } from 'lucide-react'

interface CaptionGeneratorFormProps {
  onGenerated?: () => void
}

export function CaptionGeneratorForm({ onGenerated }: CaptionGeneratorFormProps) {
  const { user } = useAuth()
  const { isGenerating, generateCaptions, reset, message, progress } = useCaptionGenerator()
  
  const [formData, setFormData] = useState({
    originalContent: '',
    contentType: 'blog_post',
    targetTone: 'professional'
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user?.id) {
      console.error('User not authenticated')
      return
    }

    if (!formData?.originalContent?.trim()) {
      return
    }

    await generateCaptions(
      formData?.originalContent,
      formData?.contentType,
      formData?.targetTone,
      user?.id
    )

    onGenerated?.()
  }

  const handleReset = () => {
    setFormData({
      originalContent: '',
      contentType: 'blog_post',
      targetTone: 'professional'
    })
    reset()
  }

  // Sample content examples
  const sampleContents = [
    {
      title: "Business Example",
      content: "After analyzing over 500 SaaS companies and their pricing models, I've discovered that the most successful businesses don't just set prices—they architect psychological experiences that make customers feel smart about their purchase decisions..."
    },
    {
      title: "Health Example", 
      content: "I used to wake up exhausted every single day, despite getting 8 hours of sleep. I tried everything—different mattresses, sleep apps, meditation, supplements. Nothing worked until I discovered I was making three critical mistakes..."
    },
    {
      title: "Tech Example",
      content: "I've spent over $3,000 and 200+ hours testing 23 different AI writing platforms for my agency, and the results will surprise you. Most AI writing tools produce content that sounds like AI wrote it..."
    }
  ]

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-blue-500" />
          Multi-Platform Caption Generator
        </CardTitle>
        <CardDescription>
          Transform your long-form content into optimized captions for Twitter, LinkedIn, Instagram, and TikTok
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="content" className="text-sm font-medium">
              Original Content *
            </label>
            <Textarea
              id="content"
              placeholder="Paste your blog post, article, or long-form content here..."
              value={formData?.originalContent ?? ''}
              onChange={(e) => setFormData(prev => ({ ...prev, originalContent: e?.target?.value ?? '' }))}
              className="min-h-[150px] resize-none"
              disabled={isGenerating}
              required
            />
            <div className="text-xs text-muted-foreground">
              {formData?.originalContent?.length ?? 0} characters
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="contentType" className="text-sm font-medium">
                Content Type
              </label>
              <Select 
                value={formData?.contentType ?? 'blog_post'} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, contentType: value ?? 'blog_post' }))}
                disabled={isGenerating}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select content type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="blog_post">Blog Post</SelectItem>
                  <SelectItem value="article">Article</SelectItem>
                  <SelectItem value="social_post">Social Post</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label htmlFor="targetTone" className="text-sm font-medium">
                Target Tone
              </label>
              <Select 
                value={formData?.targetTone ?? 'professional'} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, targetTone: value ?? 'professional' }))}
                disabled={isGenerating}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select target tone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="professional">Professional</SelectItem>
                  <SelectItem value="casual">Casual</SelectItem>
                  <SelectItem value="humorous">Humorous</SelectItem>
                  <SelectItem value="inspirational">Inspirational</SelectItem>
                  <SelectItem value="educational">Educational</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              type="submit"
              disabled={isGenerating || !formData?.originalContent?.trim()}
              className="flex-1"
            >
              {isGenerating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isGenerating ? `${message} (${progress}%)` : 'Generate Captions'}
            </Button>
            
            {(formData?.originalContent || isGenerating) && (
              <Button
                type="button"
                variant="outline"
                onClick={handleReset}
                disabled={isGenerating}
              >
                Reset
              </Button>
            )}
          </div>
        </form>

        {/* Sample Content Examples */}
        <div className="border-t pt-4">
          <h3 className="text-sm font-medium mb-3">Need inspiration? Try these examples:</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            {sampleContents?.map?.((sample, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                className="h-auto p-2 text-left justify-start"
                onClick={() => setFormData(prev => ({ 
                  ...prev, 
                  originalContent: sample?.content ?? '' 
                }))}
                disabled={isGenerating}
              >
                <div>
                  <div className="font-medium text-xs">{sample?.title}</div>
                  <div className="text-xs text-muted-foreground truncate">
                    {sample?.content?.slice?.(0, 60) ?? ''}...
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
