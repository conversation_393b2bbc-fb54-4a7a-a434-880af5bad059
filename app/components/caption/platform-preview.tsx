
'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Copy, Check, Twitter, Linkedin, Instagram, Video } from 'lucide-react'
import { trackUsage } from '@/lib/usage-tracker'
import { useAuth } from '@/components/auth/auth-provider'

interface PlatformPreviewProps {
  platform: 'twitter' | 'linkedin' | 'instagram' | 'tiktok'
  caption: string
  charCount: number
  captionId?: string
  onCopy?: () => void
}

const PLATFORM_SPECS = {
  twitter: {
    name: 'Twitter/X',
    icon: Twitter,
    maxLength: 280,
    optimalRange: [71, 100],
    color: 'text-blue-500',
    bgColor: 'bg-blue-50 dark:bg-blue-950'
  },
  linkedin: {
    name: 'LinkedIn',
    icon: Linkedin,
    maxLength: 3000,
    optimalRange: [150, 300],
    color: 'text-blue-700',
    bgColor: 'bg-blue-50 dark:bg-blue-950'
  },
  instagram: {
    name: 'Instagram',
    icon: Instagram,
    maxLength: 2200,
    optimalRange: [100, 150],
    color: 'text-pink-500',
    bgColor: 'bg-pink-50 dark:bg-pink-950'
  },
  tiktok: {
    name: 'TikTok',
    icon: Video,
    maxLength: 300,
    optimalRange: [80, 100],
    color: 'text-purple-500',
    bgColor: 'bg-purple-50 dark:bg-purple-950'
  }
}

export function PlatformPreview({ 
  platform, 
  caption, 
  charCount, 
  captionId,
  onCopy 
}: PlatformPreviewProps) {
  const { user } = useAuth()
  const [copied, setCopied] = useState(false)
  const spec = PLATFORM_SPECS[platform]
  const Icon = spec?.icon

  const getCharacterStatus = () => {
    if (charCount <= (spec?.optimalRange?.[1] ?? 0)) {
      return 'optimal'
    } else if (charCount <= spec?.maxLength) {
      return 'acceptable'
    } else {
      return 'exceeded'
    }
  }

  const getCharacterColor = () => {
    const status = getCharacterStatus()
    switch (status) {
      case 'optimal':
        return 'text-green-600'
      case 'acceptable':
        return 'text-yellow-600'
      case 'exceeded':
        return 'text-red-600'
      default:
        return 'text-muted-foreground'
    }
  }

  const handleCopy = async () => {
    try {
      await navigator?.clipboard?.writeText?.(caption)
      setCopied(true)
      
      // Track copy action
      if (user?.id && captionId) {
        await trackUsage(user?.id, 'copy', captionId, platform)
      }
      
      onCopy?.()
      
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const formatCaption = (text: string) => {
    // Split by line breaks and render with proper spacing
    return text?.split?.('\n')?.map?.((line, index) => (
      <span key={index}>
        {line}
        {index < (text?.split?.('\n')?.length ?? 0) - 1 && <br />}
      </span>
    ))
  }

  return (
    <Card className={`h-full transition-all hover:shadow-md ${spec?.bgColor}`}>
      <CardHeader className="pb-3">
        <CardTitle className={`text-sm flex items-center gap-2 ${spec?.color}`}>
          <Icon className="h-4 w-4" />
          {spec?.name}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Caption Preview */}
        <div className="bg-background/50 p-3 rounded-md min-h-[100px] text-sm leading-relaxed">
          {caption ? (
            formatCaption(caption)
          ) : (
            <span className="text-muted-foreground italic">
              Generated caption will appear here...
            </span>
          )}
        </div>

        {/* Character Count */}
        <div className="flex justify-between items-center text-xs">
          <span className={getCharacterColor()}>
            {charCount}/{spec?.maxLength} chars
          </span>
          <span className="text-muted-foreground">
            Optimal: {spec?.optimalRange?.[0]}-{spec?.optimalRange?.[1]}
          </span>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
          <div
            className={`h-1 rounded-full transition-all ${
              getCharacterStatus() === 'optimal' 
                ? 'bg-green-500' 
                : getCharacterStatus() === 'acceptable' 
                ? 'bg-yellow-500' 
                : 'bg-red-500'
            }`}
            style={{
              width: `${Math.min(100, (charCount / spec?.maxLength) * 100)}%`
            }}
          />
        </div>

        {/* Actions */}
        {caption && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopy}
              className="flex-1 h-8"
              disabled={!caption}
            >
              {copied ? (
                <>
                  <Check className="h-3 w-3 mr-1" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-3 w-3 mr-1" />
                  Copy
                </>
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
