
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/components/auth/auth-provider'
import { User, LogOut, Settings, History, Sparkles, Menu, X } from 'lucide-react'
import Link from 'next/link'

export function Header() {
  const { user, loading, profile, signOut } = useAuth()
  const [signingOut, setSigningOut] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  // Get display name - prefer username, then full name, then email
  const getDisplayName = () => {
    if (profile?.username) {
      return `@${profile.username}`
    }
    if (profile?.full_name) {
      return profile.full_name
    }
    return user?.email || 'User'
  }

  const handleSignOut = async () => {
    console.log('🚪 Header: Signing out user')
    setSigningOut(true)
    try {
      await signOut()
      console.log('✅ Header: Sign out successful')
    } catch (error) {
      console.error('❌ Header: Error signing out:', error)
    } finally {
      setSigningOut(false)
    }
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
            <Sparkles className="h-6 w-6 text-blue-500" />
            <h1 className="text-xl font-bold">CaptionCraft</h1>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-4">
            {!loading && user ? (
              <>
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/dashboard" className="flex items-center gap-2">
                    <History className="h-4 w-4" />
                    Dashboard
                  </Link>
                </Button>
                
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/settings" className="flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Settings
                  </Link>
                </Button>
                
                <div className="hidden lg:flex items-center gap-2 text-sm text-muted-foreground px-3 py-2 bg-muted/50 rounded-md">
                  <User className="h-4 w-4" />
                  <span className="max-w-[150px] truncate">{getDisplayName()}</span>
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSignOut}
                  disabled={signingOut}
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  {signingOut ? 'Signing out...' : 'Sign Out'}
                </Button>
              </>
            ) : (
              <>
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/signin">Sign In</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href="/signup">Start Free Trial</Link>
                </Button>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t bg-background/95 backdrop-blur">
            <div className="py-4 space-y-2">
              {!loading && user ? (
                <>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground px-3 py-2 bg-muted/30 rounded-md mx-2">
                    <User className="h-4 w-4" />
                    <span className="truncate">{getDisplayName()}</span>
                  </div>
                  
                  <Button variant="ghost" size="sm" asChild className="w-full justify-start">
                    <Link href="/dashboard" className="flex items-center gap-2" onClick={() => setMobileMenuOpen(false)}>
                      <History className="h-4 w-4" />
                      Dashboard
                    </Link>
                  </Button>
                  
                  <Button variant="ghost" size="sm" asChild className="w-full justify-start">
                    <Link href="/settings" className="flex items-center gap-2" onClick={() => setMobileMenuOpen(false)}>
                      <Settings className="h-4 w-4" />
                      Settings
                    </Link>
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      handleSignOut()
                      setMobileMenuOpen(false)
                    }}
                    disabled={signingOut}
                    className="w-full justify-start"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    {signingOut ? 'Signing out...' : 'Sign Out'}
                  </Button>
                </>
              ) : (
                <>
                  <Button variant="ghost" size="sm" asChild className="w-full justify-start">
                    <Link href="/signin" onClick={() => setMobileMenuOpen(false)}>Sign In</Link>
                  </Button>
                  <Button variant="ghost" size="sm" asChild className="w-full justify-start">
                    <Link href="/signup" onClick={() => setMobileMenuOpen(false)}>Start Free Trial</Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
