
'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/components/auth/auth-provider'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { CaptionGeneratorForm } from '@/components/caption/caption-generator-form'
import { CaptionResults } from '@/components/caption/caption-results'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Sparkles, Twitter, Linkedin, Instagram, Video, CheckCircle, Zap, Users, TrendingUp } from 'lucide-react'

export default function HomePage() {
  const { user, loading, session, profile } = useAuth()
  // Debug logs
  console.log('🏠 HomePage Render:', {
    loading,
    hasUser: !!user,
    hasSession: !!session,
    hasProfile: !!profile,
    userEmail: user?.email,
    userId: user?.id,
    timestamp: new Date().toISOString()
  })

  if (loading) {
    console.log('⏳ HomePage: Showing loading screen')
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Sparkles className="h-8 w-8 animate-spin mx-auto text-blue-500" />
          <p className="text-muted-foreground">Loading CaptionCraft...</p>
        </div>
      </div>
    )
  }

  // Authenticated user view
  if (user) {
    console.log('✅ HomePage: Rendering authenticated view for user:', user.email)
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 py-8">
          <div className="container max-w-6xl space-y-8">
            <CaptionGeneratorForm />
            <CaptionResults />
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  // Landing page for non-authenticated users
  console.log('🌐 HomePage: Rendering landing page for non-authenticated user')
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      {/* Hero Section */}
      <section className="flex-1 section-padding hero-gradient">
        <div className="container max-w-7xl text-center space-y-8 animate-fade-in">
          <div className="space-y-6">
            <h1 className="heading-primary text-shadow">
              Transform One Post Into{' '}
              <span className="gradient-text">Four Platform-Optimized</span>{' '}
              Captions
            </h1>
            <p className="text-body text-muted-foreground max-w-3xl mx-auto">
              Stop manually adapting your content for different social platforms. 
              Generate perfectly optimized captions for Twitter, LinkedIn, Instagram, and TikTok in seconds.
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              size="lg" 
              asChild
              className="text-lg px-8 py-6 bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all"
            >
              <Link href="/signup">
                <Sparkles className="mr-2 h-5 w-5" />
                Start Free Trial
              </Link>
            </Button>
            <p className="text-sm text-muted-foreground">
              2 Free Attempts • Then $20/month • Cancel Anytime
            </p>
          </div>

          {/* Platform Previews */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 max-w-6xl mx-auto pt-12 animate-slide-up">
            <Card className="card-hover bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2 text-blue-600 dark:text-blue-400">
                  <Twitter className="h-4 w-4" />
                  Twitter/X
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xs text-muted-foreground">71-100 chars • 2 hashtags max</p>
                <div className="mt-2 h-16 bg-background/50 rounded p-2 text-xs">
                  "Quick insights that drive engagement..."
                </div>
              </CardContent>
            </Card>

            <Card className="card-hover bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2 text-blue-700 dark:text-blue-300">
                  <Linkedin className="h-4 w-4" />
                  LinkedIn
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xs text-muted-foreground">150-300 chars • Professional tone</p>
                <div className="mt-2 h-16 bg-background/50 rounded p-2 text-xs">
                  "Professional insights with industry expertise..."
                </div>
              </CardContent>
            </Card>

            <Card className="card-hover bg-pink-50 dark:bg-pink-950 border-pink-200 dark:border-pink-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2 text-pink-600 dark:text-pink-400">
                  <Instagram className="h-4 w-4" />
                  Instagram
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xs text-muted-foreground">100-150 chars • Visual storytelling</p>
                <div className="mt-2 h-16 bg-background/50 rounded p-2 text-xs">
                  "✨ Inspiring stories with emojis..."
                </div>
              </CardContent>
            </Card>

            <Card className="card-hover bg-purple-50 dark:bg-purple-950 border-purple-200 dark:border-purple-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2 text-purple-600 dark:text-purple-400">
                  <Video className="h-4 w-4" />
                  TikTok
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xs text-muted-foreground">80-100 chars • Trending focus</p>
                <div className="mt-2 h-16 bg-background/50 rounded p-2 text-xs">
                  "POV: You found the perfect tool..."
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section-padding bg-muted/20">
        <div className="container max-w-7xl">
          <div className="text-center space-y-4 mb-16">
            <h2 className="heading-secondary">
              Why Content Creators Love CaptionCraft
            </h2>
            <p className="text-body text-muted-foreground max-w-3xl mx-auto">
              Save hours of manual work and never worry about platform-specific requirements again
            </p>
          </div>

          <div className="grid-responsive">
            <Card className="card-hover text-center">
              <CardHeader>
                <Zap className="h-12 w-12 mx-auto text-primary mb-4" />
                <CardTitle>Lightning Fast</CardTitle>
                <CardDescription>
                  Generate 4 platform-optimized captions in under 10 seconds
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="card-hover text-center">
              <CardHeader>
                <Users className="h-12 w-12 mx-auto text-primary mb-4" />
                <CardTitle>Platform Native</CardTitle>
                <CardDescription>
                  Each caption follows 2025 best practices for maximum engagement
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="card-hover text-center">
              <CardHeader>
                <TrendingUp className="h-12 w-12 mx-auto text-primary mb-4" />
                <CardTitle>Proven Results</CardTitle>
                <CardDescription>
                  Based on analysis of 500+ successful social media campaigns
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="section-padding">
        <div className="container max-w-5xl text-center space-y-8">
          <div className="space-y-4">
            <h2 className="heading-secondary">
              Simple, Transparent Pricing
            </h2>
            <p className="text-body text-muted-foreground">
              Start free, scale as you grow
            </p>
          </div>

          <Card className="max-w-md mx-auto card-hover border-2 border-primary">
            <CardHeader>
              <CardTitle className="text-2xl">Pro Plan</CardTitle>
              <div className="text-4xl font-bold text-primary">
                $20<span className="text-lg text-muted-foreground">/month</span>
              </div>
              <CardDescription>
                Everything you need to scale your social media presence
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3 text-left">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                  <span>Unlimited caption generations</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                  <span>All 4 platforms (Twitter, LinkedIn, Instagram, TikTok)</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                  <span>Save and manage caption history</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                  <span>5 different tone options</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                  <span>Real-time character counting</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                  <span>Priority support</span>
                </div>
              </div>
              
              <Button 
                size="lg" 
                className="w-full"
                asChild
              >
                <Link href="/signup">
                  Start Free Trial
                </Link>
              </Button>
              
              <p className="text-xs text-muted-foreground">
                2 free generations • No credit card required • Cancel anytime
              </p>
            </CardContent>
          </Card>
        </div>
      </section>



      <Footer />
    </div>
  )
}
