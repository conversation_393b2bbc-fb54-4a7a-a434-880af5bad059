
'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth/auth-provider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, Eye, EyeOff, Sparkles, ArrowLeft, CheckCircle } from 'lucide-react'
import Link from 'next/link'

export default function SignUpPage() {
  const router = useRouter()
  const { user, signUp, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [formData, setFormData] = useState({
    fullName: '',
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  })
  const [errors, setErrors] = useState<string[]>([])
  const [success, setSuccess] = useState('')

  // Redirect if already signed in
  useEffect(() => {
    if (!authLoading && user) {
      console.log('🔄 SignUpPage: User is already authenticated, redirecting to main page')
      router.push('/')
    }
  }, [user, authLoading, router])

  // Don't render if still loading or user is authenticated
  if (authLoading || user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Sparkles className="h-8 w-8 animate-spin mx-auto text-blue-500" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  const validateForm = () => {
    const newErrors: string[] = []
    
    if (!formData.fullName.trim()) newErrors.push('Full name is required')
    if (!formData.username.trim()) newErrors.push('Username is required')
    if (formData.username.length < 3) newErrors.push('Username must be at least 3 characters')
    if (!formData.email.trim()) newErrors.push('Email is required')
    if (!formData.email.includes('@')) newErrors.push('Please enter a valid email address')
    if (formData.password.length < 6) newErrors.push('Password must be at least 6 characters')
    if (formData.password !== formData.confirmPassword) newErrors.push('Passwords do not match')
    
    return newErrors
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setErrors([])
    setSuccess('')

    console.log('📝 SignUpPage: Attempting to sign up user:', formData.email)

    const validationErrors = validateForm()
    if (validationErrors.length > 0) {
      console.error('❌ SignUpPage: Validation errors:', validationErrors)
      setErrors(validationErrors)
      setLoading(false)
      return
    }

    try {
      const { error } = await signUp(
        formData.email, 
        formData.password, 
        {
          full_name: formData.fullName,
          username: formData.username.toLowerCase().trim()
        }
      )
      
      if (error) {
        console.error('❌ SignUpPage: Sign up failed:', error)
        setErrors([error.message || 'Failed to create account. Please try again.'])
      } else {
        console.log('✅ SignUpPage: Sign up successful')
        setSuccess('Account created successfully! You can now sign in.')
        setTimeout(() => {
          router.push('/signin')
        }, 2000)
      }
    } catch (error) {
      console.error('💥 SignUpPage: Sign up exception:', error)
      setErrors([error instanceof Error ? error.message : 'An error occurred during sign up'])
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-950 dark:via-blue-950 dark:to-indigo-950 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="h-8 w-8 text-blue-500" />
            <h1 className="text-2xl font-bold">CaptionCraft</h1>
          </div>
          <h2 className="text-3xl font-bold">Create Account</h2>
          <p className="text-muted-foreground">
            Get started with your free trial - 2 caption sets included
          </p>
        </div>

        {/* Sign Up Form */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>Sign Up</CardTitle>
            <CardDescription>
              Create your account to start generating platform-optimized captions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="fullName" className="text-sm font-medium">
                  Full Name
                </label>
                <Input
                  id="fullName"
                  type="text"
                  placeholder="Enter your full name"
                  value={formData.fullName}
                  onChange={(e) => setFormData(prev => ({ ...prev, fullName: e.target.value }))}
                  required
                  disabled={loading}
                  className="h-11"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="username" className="text-sm font-medium">
                  Username
                </label>
                <Input
                  id="username"
                  type="text"
                  placeholder="Choose a username"
                  value={formData.username}
                  onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                  required
                  disabled={loading}
                  className="h-11"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">
                  Email
                </label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email address"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  required
                  disabled={loading}
                  className="h-11"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium">
                  Password
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Create a password"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    required
                    disabled={loading}
                    className="h-11 pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-11 w-11 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={loading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="confirmPassword" className="text-sm font-medium">
                  Confirm Password
                </label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="Confirm your password"
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    required
                    disabled={loading}
                    className="h-11 pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-11 w-11 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={loading}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {errors.length > 0 && (
                <div className="text-sm space-y-1">
                  {errors.map((error, index) => (
                    <div key={index} className="text-red-600 bg-red-50 dark:bg-red-950 p-3 rounded-md">
                      {error}
                    </div>
                  ))}
                </div>
              )}

              {success && (
                <div className="text-sm text-green-600 bg-green-50 dark:bg-green-950 p-3 rounded-md flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  {success}
                </div>
              )}

              <Button
                type="submit"
                className="w-full h-11"
                disabled={loading}
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create Account
              </Button>
            </form>

            <div className="mt-6 space-y-4">
              <div className="text-center">
                <span className="text-sm text-muted-foreground">
                  Already have an account?{' '}
                </span>
                <Link 
                  href="/signin" 
                  className="text-sm text-primary hover:text-primary/90 font-medium"
                >
                  Sign in
                </Link>
              </div>

              <div className="text-center">
                <Link 
                  href="/" 
                  className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back to home
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
