
'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth/auth-provider'
import { supabase } from '@/lib/supabase'
import { supabaseAdmin } from '@/lib/supabase-admin'

export default function DebugAuthPage() {
  const { user, loading, session, profile } = useAuth()
  const [debugInfo, setDebugInfo] = useState<any>({})
  const [testResults, setTestResults] = useState<any>({})

  const runDiagnosticTests = async () => {
    console.log('🔬 Starting comprehensive auth diagnostics...')
    
    const tests: any = {
      timestamp: new Date().toISOString(),
      page: 'debug-auth',
      authProviderState: {
        hasUser: !!user,
        hasSession: !!session,
        hasProfile: !!profile,
        loading,
        userEmail: user?.email,
        userId: user?.id
      }
    }

    // Test 1: Direct session check
    console.log('🧪 Test 1: Direct session check')
    try {
      const { data: { session: directSession }, error } = await supabase.auth.getSession()
      tests.directSessionCheck = {
        success: true,
        hasSession: !!directSession,
        hasUser: !!directSession?.user,
        userEmail: directSession?.user?.email,
        userId: directSession?.user?.id,
        error: error?.message
      }
      console.log('✅ Test 1 passed:', tests.directSessionCheck)
    } catch (error) {
      tests.directSessionCheck = { success: false, error: (error as Error)?.message || 'Unknown error' }
      console.log('❌ Test 1 failed:', error)
    }

    // Test 2: Profile fetch with regular client
    if (tests.directSessionCheck?.userId) {
      console.log('🧪 Test 2: Profile fetch with regular client')
      try {
        const startTime = Date.now()
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', tests.directSessionCheck.userId)
          .single()
        
        const endTime = Date.now()
        tests.regularClientProfileFetch = {
          success: !error,
          duration: endTime - startTime,
          hasData: !!data,
          username: data?.username,
          error: error?.message,
          errorCode: error?.code
        }
        console.log('✅ Test 2 result:', tests.regularClientProfileFetch)
      } catch (error) {
        tests.regularClientProfileFetch = { success: false, error: (error as Error)?.message || 'Unknown error' }
        console.log('❌ Test 2 failed:', error)
      }

      // Test 3: Profile fetch with admin client
      console.log('🧪 Test 3: Profile fetch with admin client')
      try {
        const startTime = Date.now()
        const { data, error } = await supabaseAdmin
          .from('profiles')
          .select('*')
          .eq('id', tests.directSessionCheck.userId)
          .single()
        
        const endTime = Date.now()
        tests.adminClientProfileFetch = {
          success: !error,
          duration: endTime - startTime,
          hasData: !!data,
          username: data?.username,
          error: error?.message,
          errorCode: error?.code
        }
        console.log('✅ Test 3 result:', tests.adminClientProfileFetch)
      } catch (error) {
        tests.adminClientProfileFetch = { success: false, error: (error as Error)?.message || 'Unknown error' }
        console.log('❌ Test 3 failed:', error)
      }
    }

    // Test 4: Network connectivity
    console.log('🧪 Test 4: Network connectivity test')
    try {
      const startTime = Date.now()
      const { data, error } = await supabase
        .from('profiles')
        .select('count')
        .limit(1)
      
      const endTime = Date.now()
      tests.networkConnectivity = {
        success: !error,
        duration: endTime - startTime,
        error: error?.message
      }
      console.log('✅ Test 4 result:', tests.networkConnectivity)
    } catch (error) {
      tests.networkConnectivity = { success: false, error: (error as Error)?.message || 'Unknown error' }
      console.log('❌ Test 4 failed:', error)
    }

    console.log('🔬 All diagnostic tests completed:', tests)
    setTestResults(tests)
  }

  useEffect(() => {
    const info = {
      timestamp: new Date().toISOString(),
      page: 'debug-auth',
      userAgent: navigator.userAgent,
      url: window.location.href,
      authProvider: {
        hasUser: !!user,
        hasSession: !!session,
        hasProfile: !!profile,
        loading,
        userEmail: user?.email,
        userId: user?.id
      }
    }
    
    setDebugInfo(info)
    console.log('🏷️ Debug page info:', info)
  }, [user, session, profile, loading])

  return (
    <div className="min-h-screen p-8 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold">🔬 Authentication Debug Panel</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">AuthProvider State</h2>
            <pre className="text-xs overflow-auto bg-gray-100 dark:bg-gray-700 p-4 rounded">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Diagnostic Tests</h2>
              <button 
                onClick={runDiagnosticTests}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Run Tests
              </button>
            </div>
            <pre className="text-xs overflow-auto bg-gray-100 dark:bg-gray-700 p-4 rounded max-h-96">
              {JSON.stringify(testResults, null, 2)}
            </pre>
          </div>
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 p-4 rounded-lg">
          <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
            🚨 Instructions for Human Diagnosis
          </h3>
          <div className="text-yellow-700 dark:text-yellow-300 space-y-2">
            <p><strong>1.</strong> Open browser console (F12)</p>
            <p><strong>2.</strong> Visit this debug page: <code>/debug-auth</code></p>
            <p><strong>3.</strong> Click "Run Tests" button above</p>
            <p><strong>4.</strong> Compare results with main page behavior</p>
            <p><strong>5.</strong> Check if regular client vs admin client makes a difference</p>
            <p><strong>6.</strong> Look for timing differences between tests</p>
          </div>
        </div>

        <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 p-4 rounded-lg">
          <h3 className="font-semibold text-red-800 dark:text-red-200 mb-2">
            🔍 What to Look For:
          </h3>
          <ul className="text-red-700 dark:text-red-300 list-disc list-inside space-y-1">
            <li>Does regular client profile fetch hang here too?</li>
            <li>Does admin client work better?</li>
            <li>Are there timing/duration differences?</li>
            <li>Any RLS (Row Level Security) errors?</li>
            <li>Network connectivity issues?</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
