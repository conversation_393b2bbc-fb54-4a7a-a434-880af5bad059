
'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/components/auth/auth-provider'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { supabase } from '@/lib/supabase'
import { Loader2, User, Mail, CreditCard, Shield, Trash2, Settings as SettingsIcon } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface UserProfile {
  id: string
  email: string
  username?: string
  full_name: string
  subscription_status: string
  subscription_plan: string
  trial_uses_remaining: number
  created_at: string
}

export default function SettingsPage() {
  const { user, loading, profile: authProfile } = useAuth()
  const router = useRouter()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [profileLoading, setProfileLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [formData, setFormData] = useState({
    fullName: '',
    username: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [errors, setErrors] = useState<string[]>([])
  const [success, setSuccess] = useState('')

  useEffect(() => {
    if (user?.id) {
      loadProfile()
    }
  }, [user?.id])

  const loadProfile = async () => {
    if (!user?.id) return

    setProfileLoading(true)
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error) {
        console.error('Error loading profile:', error)
      } else {
        setProfile(data)
        setFormData(prev => ({ 
          ...prev, 
          fullName: data.full_name || '',
          username: data.username || ''
        }))
      }
    } catch (error) {
      console.error('Error loading profile:', error)
    } finally {
      setProfileLoading(false)
    }
  }

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    setUpdating(true)
    setErrors([])
    setSuccess('')

    try {
      // Update profile in Supabase
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: formData.fullName,
          username: formData.username || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', user?.id)

      if (error) {
        setErrors([error.message])
      } else {
        setSuccess('Profile updated successfully!')
        await loadProfile() // Reload profile data
      }
    } catch (error) {
      setErrors([error instanceof Error ? error.message : 'An error occurred'])
    } finally {
      setUpdating(false)
    }
  }

  const handleUpdatePassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setUpdating(true)
    setErrors([])
    setSuccess('')

    if (formData.newPassword !== formData.confirmPassword) {
      setErrors(['New passwords do not match'])
      setUpdating(false)
      return
    }

    if (formData.newPassword.length < 6) {
      setErrors(['Password must be at least 6 characters long'])
      setUpdating(false)
      return
    }

    try {
      const { error } = await supabase.auth.updateUser({
        password: formData.newPassword
      })

      if (error) {
        setErrors([error.message])
      } else {
        setSuccess('Password updated successfully!')
        setFormData(prev => ({
          ...prev,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        }))
      }
    } catch (error) {
      setErrors([error instanceof Error ? error.message : 'An error occurred'])
    } finally {
      setUpdating(false)
    }
  }

  const getSubscriptionBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-500 hover:bg-green-600">Active</Badge>
      case 'trial':
        return <Badge variant="secondary">Free Trial</Badge>
      case 'expired':
        return <Badge variant="destructive">Expired</Badge>
      case 'cancelled':
        return <Badge variant="outline">Cancelled</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  if (loading || profileLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
          <p className="text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    router.push('/signin')
    return null
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen flex flex-col">
        <Header />
      
      <main className="flex-1 py-8">
        <div className="container max-w-4xl space-y-8">
          {/* Header */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <SettingsIcon className="h-6 w-6" />
              <h1 className="text-3xl font-bold">Settings</h1>
            </div>
            <p className="text-muted-foreground">
              Manage your account settings and preferences
            </p>
          </div>

          {/* Account Information */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <User className="h-5 w-5" />
                <CardTitle>Account Information</CardTitle>
              </div>
              <CardDescription>
                View and update your account details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <div className="flex items-center gap-2 mt-1">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{profile?.email}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Username</label>
                  <div className="flex items-center gap-2 mt-1">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{profile?.username ? `@${profile.username}` : 'Not set'}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Full Name</label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {profile?.full_name || 'Not set'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Member Since</label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {profile?.created_at ? new Date(profile.created_at).toLocaleDateString() : 'Unknown'}
                  </p>
                </div>
              </div>

              <Separator />

              <form onSubmit={handleUpdateProfile} className="space-y-4">
                <div>
                  <label htmlFor="fullName" className="text-sm font-medium">
                    Full Name
                  </label>
                  <Input
                    id="fullName"
                    type="text"
                    value={formData.fullName}
                    onChange={(e) => setFormData(prev => ({ ...prev, fullName: e.target.value }))}
                    disabled={updating}
                    className="mt-1"
                    placeholder="Enter your full name"
                  />
                </div>

                <div>
                  <label htmlFor="username" className="text-sm font-medium">
                    Username
                  </label>
                  <Input
                    id="username"
                    type="text"
                    value={formData.username}
                    onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                    disabled={updating}
                    className="mt-1"
                    placeholder="Choose a unique username"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    You can sign in using your username instead of email
                  </p>
                </div>

                <Button type="submit" disabled={updating}>
                  {updating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Update Profile
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Subscription Information */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                <CardTitle>Subscription</CardTitle>
              </div>
              <CardDescription>
                Your current subscription status and usage
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Status</p>
                  <p className="text-sm text-muted-foreground">Your current subscription status</p>
                </div>
                {getSubscriptionBadge(profile?.subscription_status || 'trial')}
              </div>

              {profile?.subscription_status === 'trial' && (
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Trial Uses Remaining</p>
                    <p className="text-sm text-muted-foreground">Free caption generations left</p>
                  </div>
                  <Badge variant="outline">{profile.trial_uses_remaining}/2</Badge>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Plan</p>
                  <p className="text-sm text-muted-foreground">Your current plan</p>
                </div>
                <Badge variant="outline" className="capitalize">
                  {profile?.subscription_plan === 'free' ? 'Free Trial' : profile?.subscription_plan}
                </Badge>
              </div>

              {profile?.subscription_status === 'trial' && (
                <div className="p-4 bg-muted/50 rounded-lg">
                  <p className="font-medium mb-2">Upgrade to Pro</p>
                  <p className="text-sm text-muted-foreground mb-4">
                    Get unlimited caption generations for just $20/month
                  </p>
                  <Button>Upgrade Now</Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Security */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                <CardTitle>Security</CardTitle>
              </div>
              <CardDescription>
                Update your password and security settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleUpdatePassword} className="space-y-4">
                <div>
                  <label htmlFor="newPassword" className="text-sm font-medium">
                    New Password
                  </label>
                  <Input
                    id="newPassword"
                    type="password"
                    value={formData.newPassword}
                    onChange={(e) => setFormData(prev => ({ ...prev, newPassword: e.target.value }))}
                    disabled={updating}
                    className="mt-1"
                    placeholder="Enter new password"
                  />
                </div>

                <div>
                  <label htmlFor="confirmPassword" className="text-sm font-medium">
                    Confirm New Password
                  </label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    disabled={updating}
                    className="mt-1"
                    placeholder="Confirm new password"
                  />
                </div>

                <Button type="submit" disabled={updating}>
                  {updating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Update Password
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Messages */}
          {(errors.length > 0 || success) && (
            <div className="space-y-2">
              {errors.map((error, index) => (
                <div key={index} className="text-sm text-red-600 bg-red-50 dark:bg-red-950 p-3 rounded-md">
                  {error}
                </div>
              ))}
              {success && (
                <div className="text-sm text-green-600 bg-green-50 dark:bg-green-950 p-3 rounded-md">
                  {success}
                </div>
              )}
            </div>
          )}
        </div>
      </main>

        <Footer />
      </div>
    </ProtectedRoute>
  )
}
