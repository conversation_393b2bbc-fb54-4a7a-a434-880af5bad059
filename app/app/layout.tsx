
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '@/components/auth/auth-provider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'CaptionCraft - Multi-Platform Caption Repurposer',
  description: 'Transform one post into four platform-optimized captions for Twitter, LinkedIn, Instagram, and TikTok. Free trial with 2 attempts, then $20/month.',
  keywords: 'social media, captions, content creation, Twitter, LinkedIn, Instagram, TikTok, AI, marketing',
  openGraph: {
    title: 'CaptionCraft - Multi-Platform Caption Repurposer',
    description: 'Transform one post into four platform-optimized captions instantly',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'CaptionCraft - Multi-Platform Caption Repurposer',
    description: 'Transform one post into four platform-optimized captions instantly',
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter?.className} antialiased min-h-screen bg-background`}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}
