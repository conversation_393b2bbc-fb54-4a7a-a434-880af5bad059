
'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth/auth-provider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, Eye, EyeOff, Sparkles, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function SignInPage() {
  const router = useRouter()
  const { user, signIn, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    emailOrUsername: '',
    password: ''
  })
  const [error, setError] = useState('')

  // Redirect if already signed in
  useEffect(() => {
    if (!authLoading && user) {
      console.log('🔄 SignInPage: User is already authenticated, redirecting to main page')
      router.push('/')
    }
  }, [user, authLoading, router])

  // Don't render if still loading or user is authenticated
  if (authLoading || user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Sparkles className="h-8 w-8 animate-spin mx-auto text-blue-500" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    console.log('🔑 SignInPage: Attempting to sign in with:', formData.emailOrUsername)

    try {
      // Try signing in with email first, then username if that fails
      let email = formData.emailOrUsername
      
      // If it doesn't contain @, treat it as username and try to find the email
      if (!email.includes('@')) {
        console.log('👤 SignInPage: Looking up username:', email)
        try {
          const response = await fetch('/api/auth/find-user-by-username', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username: email })
          })
          
          if (response.ok) {
            const data = await response.json()
            email = data.email
            console.log('✅ SignInPage: Username lookup successful, using email:', email)
          } else {
            const errorData = await response.json()
            console.error('❌ SignInPage: Username lookup failed:', errorData)
            setError('Username not found. Please check your username or use your email address.')
            return
          }
        } catch (usernameError) {
          console.error('💥 SignInPage: Username lookup exception:', usernameError)
          setError('Error looking up username. Please try using your email address.')
          return
        }
      }

      console.log('🔐 SignInPage: Attempting Supabase sign in with email:', email)
      const { error: signInError } = await signIn(email, formData.password)
      
      if (signInError) {
        console.error('❌ SignInPage: Sign in failed:', signInError)
        setError(signInError.message || 'Invalid email/username or password. Please try again.')
      } else {
        console.log('✅ SignInPage: Sign in successful, redirecting')
        router.push('/')
      }
    } catch (error) {
      console.error('💥 SignInPage: Sign in exception:', error)
      setError(error instanceof Error ? error.message : 'An error occurred during sign in')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-950 dark:via-blue-950 dark:to-indigo-950 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="h-8 w-8 text-blue-500" />
            <h1 className="text-2xl font-bold">CaptionCraft</h1>
          </div>
          <h2 className="text-3xl font-bold">Welcome Back</h2>
          <p className="text-muted-foreground">
            Sign in to continue generating captions
          </p>
        </div>

        {/* Sign In Form */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>Sign In</CardTitle>
            <CardDescription>
              Enter your email or username and password to access your account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="emailOrUsername" className="text-sm font-medium">
                  Email or Username
                </label>
                <Input
                  id="emailOrUsername"
                  type="text"
                  placeholder="Enter your email or username"
                  value={formData.emailOrUsername}
                  onChange={(e) => setFormData(prev => ({ ...prev, emailOrUsername: e.target.value }))}
                  required
                  disabled={loading}
                  className="h-11"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium">
                  Password
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    required
                    disabled={loading}
                    className="h-11 pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-11 w-11 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={loading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {error && (
                <div className="text-sm text-red-600 bg-red-50 dark:bg-red-950 p-3 rounded-md">
                  {error}
                </div>
              )}

              <Button
                type="submit"
                className="w-full h-11"
                disabled={loading}
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Sign In
              </Button>
            </form>

            <div className="mt-6 space-y-4">
              <div className="text-center">
                <span className="text-sm text-muted-foreground">
                  Don't have an account?{' '}
                </span>
                <Link 
                  href="/signup" 
                  className="text-sm text-primary hover:text-primary/90 font-medium"
                >
                  Sign up for free
                </Link>
              </div>

              <div className="text-center">
                <Link 
                  href="/" 
                  className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back to home
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
