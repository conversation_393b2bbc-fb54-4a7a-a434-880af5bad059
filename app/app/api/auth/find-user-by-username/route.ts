
import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

export const dynamic = "force-dynamic"

export async function POST(request: NextRequest) {
  try {
    const { username } = await request.json()

    if (!username) {
      console.error('Username lookup: No username provided')
      return NextResponse.json(
        { error: 'Username is required' },
        { status: 400 }
      )
    }

    // Clean and normalize username
    const cleanUsername = username.toString().toLowerCase().trim()
    
    console.log('Looking for username:', cleanUsername)

    // Use admin client to bypass RLS
    const queries = [
      // Exact match
      supabaseAdmin.from('profiles').select('email, username').eq('username', cleanUsername).maybeSingle(),
      // Case-insensitive match
      supabaseAdmin.from('profiles').select('email, username').ilike('username', cleanUsername).maybeSingle()
    ]

    let profile = null
    let lastError = null

    for (const query of queries) {
      const { data, error } = await query
      
      console.log('Query result:', { data, error })
      
      if (error) {
        lastError = error
        console.log('Query failed with error:', error)
        continue
      }
      
      if (data) {
        profile = data
        break
      }
    }

    if (!profile) {
      console.log('User not found with username:', cleanUsername, 'Last error:', lastError)
      return NextResponse.json(
        { error: 'Username not found. Please check your username or try using your email address.' },
        { status: 404 }
      )
    }

    if (!profile.email) {
      console.log('Profile found but no email:', profile)
      return NextResponse.json(
        { error: 'User profile incomplete. Please contact support.' },
        { status: 404 }
      )
    }

    console.log('Username lookup successful:', { username: cleanUsername, email: profile.email })
    return NextResponse.json({ email: profile.email })

  } catch (error) {
    console.error('Find user by username error:', error)
    return NextResponse.json(
      { error: 'Internal server error. Please try again or use your email address.' },
      { status: 500 }
    )
  }
}
