
import { NextRequest, NextResponse } from 'next/server'
import { generateCaptions } from '@/lib/caption-generator'
import { checkUsageLimit, trackUsage } from '@/lib/usage-tracker'
import { supabase } from '@/lib/supabase'

export const dynamic = "force-dynamic"

export async function POST(request: NextRequest) {
  try {
    const { originalContent, contentType, targetTone, userId } = await request.json()

    if (!originalContent || !userId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check usage limits
    const { canGenerate, trialUsesRemaining, subscriptionStatus } = await checkUsageLimit(userId)
    
    if (!canGenerate) {
      return NextResponse.json(
        { 
          error: 'Usage limit reached', 
          trialUsesRemaining: 0,
          subscriptionStatus,
          requiresUpgrade: true 
        },
        { status: 403 }
      )
    }

    // Create a streaming response
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder()
        
        try {
          // Send initial status
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({
            status: 'processing',
            message: 'Generating captions for all platforms...',
            progress: 0
          })}\n\n`))

          // Generate captions
          const results = await generateCaptions(originalContent, contentType || 'blog_post', targetTone || 'professional')
          
          // Save to database
          const { data: captionData, error: dbError } = await supabase
            .from('caption_generations')
            .insert({
              user_id: userId,
              original_content: originalContent,
              content_type: contentType || 'blog_post',
              target_tone: targetTone || 'professional',
              twitter_caption: results.twitter.caption,
              twitter_char_count: results.twitter.charCount,
              linkedin_caption: results.linkedin.caption,
              linkedin_char_count: results.linkedin.charCount,
              instagram_caption: results.instagram.caption,
              instagram_char_count: results.instagram.charCount,
              tiktok_caption: results.tiktok.caption,
              tiktok_char_count: results.tiktok.charCount,
              generation_time_ms: results.generationTime,
              is_saved: false
            })
            .select()
            .single()

          if (dbError) {
            console.error('Database error:', dbError)
            throw new Error('Failed to save captions')
          }

          // Track usage
          await trackUsage(userId, 'generation', captionData?.id)

          // Send progress update
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({
            status: 'processing',
            message: 'Finalizing results...',
            progress: 90
          })}\n\n`))

          // Send final results
          const finalData = JSON.stringify({
            status: 'completed',
            data: {
              id: captionData?.id,
              ...results,
              usageInfo: {
                trialUsesRemaining: Math.max(0, trialUsesRemaining - 1),
                subscriptionStatus
              }
            }
          })
          
          controller.enqueue(encoder.encode(`data: ${finalData}\n\n`))
          
        } catch (error) {
          console.error('Generation error:', error)
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({
            status: 'error',
            message: error instanceof Error ? error.message : 'Failed to generate captions'
          })}\n\n`))
        } finally {
          controller.close()
        }
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      }
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
