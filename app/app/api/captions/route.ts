
import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export const dynamic = "force-dynamic"

// Get user's caption history
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const limit = searchParams.get('limit') || '10'

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    const { data: captions, error } = await supabase
      .from('caption_generations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(parseInt(limit))

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch captions' },
        { status: 500 }
      )
    }

    return NextResponse.json({ captions })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Save a caption generation
export async function POST(request: NextRequest) {
  try {
    const { captionId, userId } = await request.json()

    if (!captionId || !userId) {
      return NextResponse.json(
        { error: 'Caption ID and User ID are required' },
        { status: 400 }
      )
    }

    const { error } = await supabase
      .from('caption_generations')
      .update({ is_saved: true })
      .eq('id', captionId)
      .eq('user_id', userId)

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to save caption' },
        { status: 500 }
      )
    }

    // Track the save action
    const { trackUsage } = await import('@/lib/usage-tracker')
    await trackUsage(userId, 'save', captionId)

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Delete a caption generation
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const captionId = searchParams.get('captionId')
    const userId = searchParams.get('userId')

    if (!captionId || !userId) {
      return NextResponse.json(
        { error: 'Caption ID and User ID are required' },
        { status: 400 }
      )
    }

    const { error } = await supabase
      .from('caption_generations')
      .delete()
      .eq('id', captionId)
      .eq('user_id', userId)

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to delete caption' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
