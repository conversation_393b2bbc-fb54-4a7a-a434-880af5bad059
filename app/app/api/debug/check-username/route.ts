
import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

export const dynamic = "force-dynamic"

export async function POST(request: NextRequest) {
  try {
    const { username } = await request.json()

    if (!username) {
      return NextResponse.json(
        { error: 'Username is required' },
        { status: 400 }
      )
    }

    // Check all variations of the username
    const variations = [
      username,
      username.toLowerCase(),
      username.toLowerCase().trim(),
      username.trim()
    ]

    const results = []

    for (const variant of variations) {
      const { data, error } = await supabaseAdmin
        .from('profiles')
        .select('email, username, full_name, created_at')
        .eq('username', variant)
        .single()

      results.push({
        variant,
        found: !!data && !error,
        data: data || null,
        error: error?.code || null
      })
    }

    // Also check if any usernames contain the search term
    const { data: allUsers } = await supabaseAdmin
      .from('profiles')
      .select('username, email')
      .ilike('username', `%${username}%`)

    return NextResponse.json({
      searchTerm: username,
      exactMatches: results,
      partialMatches: allUsers || []
    })

  } catch (error) {
    console.error('Debug username check error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
