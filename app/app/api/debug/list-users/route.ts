
import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

export const dynamic = "force-dynamic"

export async function GET(request: NextRequest) {
  try {
    // Get all profiles
    const { data: profiles, error } = await supabaseAdmin
      .from('profiles')
      .select('id, email, username, full_name, created_at')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching profiles:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Get count of auth users (if accessible)
    const { count: authUsersCount } = await supabaseAdmin
      .from('profiles')
      .select('*', { count: 'exact', head: true })

    return NextResponse.json({
      profiles: profiles || [],
      totalCount: authUsersCount || 0,
      message: profiles?.length ? `Found ${profiles.length} users` : 'No users found in profiles table'
    })

  } catch (error) {
    console.error('List users error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
