
import { NextRequest, NextResponse } from 'next/server'
import { getUserStats, checkUsageLimit } from '@/lib/usage-tracker'
import { supabase } from '@/lib/supabase'

export const dynamic = "force-dynamic"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (profileError) {
      console.error('Profile error:', profileError)
      return NextResponse.json(
        { error: 'Failed to fetch profile' },
        { status: 500 }
      )
    }

    // Get usage stats
    const stats = await getUserStats(userId)
    
    // Get usage limits
    const { canGenerate, trialUsesRemaining, subscriptionStatus } = await checkUsageLimit(userId)

    return NextResponse.json({
      profile,
      stats,
      usage: {
        canGenerate,
        trialUsesRemaining,
        subscriptionStatus
      }
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
