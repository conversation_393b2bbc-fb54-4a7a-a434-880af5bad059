@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 263.4 70% 50.4%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted/20;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

@layer components {
  /* Hero gradient background */
  .hero-gradient {
    background: linear-gradient(135deg, 
      hsl(var(--background)) 0%,
      hsl(263.4, 70%, 15%) 50%,
      hsl(var(--background)) 100%
    );
  }
  
  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-blue-400 via-purple-400 to-blue-600 bg-clip-text text-transparent;
  }
  
  /* Text shadow for better readability */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
  }
  
  /* Animation classes */
  .animate-fade-in {
    animation: fade-in 0.6s ease-out;
  }
  
  .animate-slide-up {
    animation: slide-up 0.8s ease-out 0.2s both;
  }
  
  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }
  
  /* Custom container for better responsive design */
  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* Form improvements */
  .form-input {
    @apply h-11 px-3 py-2 border border-input bg-background text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  /* Button improvements */
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 shadow-md hover:shadow-lg transition-all duration-200;
  }
  
  /* Responsive grid improvements */
  .grid-responsive {
    @apply grid gap-4 sm:gap-6;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
  
  /* Card improvements for mobile */
  .card-mobile {
    @apply p-4 rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow;
  }
  
  /* Better spacing for mobile */
  .section-padding {
    @apply py-12 sm:py-16 lg:py-20;
  }
  
  /* Typography improvements */
  .heading-primary {
    @apply text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight;
  }
  
  .heading-secondary {
    @apply text-2xl sm:text-3xl lg:text-4xl font-bold;
  }
  
  .text-body {
    @apply text-base sm:text-lg leading-relaxed;
  }
  
  /* Character count colors */
  .char-optimal {
    @apply text-green-500;
  }
  
  .char-warning {
    @apply text-yellow-500;
  }
  
  .char-danger {
    @apply text-red-500;
  }
}

@layer utilities {
  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Keyframe animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Loading spinner */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner {
  border: 2px solid transparent;
  border-top: 2px solid hsl(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Focus improvements */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-up,
  .animate-pulse-slow {
    animation: none;
  }
}