
'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/components/auth/auth-provider'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { getUserStats } from '@/lib/usage-tracker'
import { supabase } from '@/lib/supabase'
import { History, Sparkles, TrendingUp, Calendar, Trash2 } from 'lucide-react'
import type { CaptionGeneration } from '@/lib/supabase'

interface UserStats {
  totalCaptionsGenerated: number
  thisMonthGenerations: number
  savedCaptions: number
}

interface UsageInfo {
  trialUsesRemaining: number
  subscriptionStatus: string
  canGenerate: boolean
}

export default function DashboardPage() {
  const { user, loading } = useAuth()
  const [stats, setStats] = useState<UserStats>({
    totalCaptionsGenerated: 0,
    thisMonthGenerations: 0,
    savedCaptions: 0
  })
  const [usageInfo, setUsageInfo] = useState<UsageInfo>({
    trialUsesRemaining: 2,
    subscriptionStatus: 'trial',
    canGenerate: true
  })
  const [recentCaptions, setRecentCaptions] = useState<CaptionGeneration[]>([])
  const [loadingStats, setLoadingStats] = useState(true)

  useEffect(() => {
    if (user?.id) {
      loadUserData()
    }
  }, [user?.id])

  const loadUserData = async () => {
    if (!user?.id) return

    setLoadingStats(true)
    try {
      // Get user stats
      const userStats = await getUserStats(user.id)
      setStats(userStats)

      // Get usage info
      const response = await fetch(`/api/user/stats?userId=${user.id}`)
      if (response?.ok) {
        const data = await response?.json?.()
        setUsageInfo(data?.usage ?? usageInfo)
      }

      // Get recent captions
      const { data: captions } = await supabase
        .from('caption_generations')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(5)

      setRecentCaptions(captions ?? [])
    } catch (error) {
      console.error('Error loading user data:', error)
    } finally {
      setLoadingStats(false)
    }
  }

  const handleDeleteCaption = async (captionId: string) => {
    if (!user?.id) return

    try {
      const response = await fetch(`/api/captions?captionId=${captionId}&userId=${user.id}`, {
        method: 'DELETE'
      })

      if (response?.ok) {
        setRecentCaptions(prev => prev?.filter?.(caption => caption?.id !== captionId) ?? [])
        await loadUserData() // Refresh stats
      }
    } catch (error) {
      console.error('Error deleting caption:', error)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString)?.toLocaleDateString?.('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }) ?? ''
  }

  const truncateText = (text: string, maxLength: number) => {
    return text?.length > maxLength ? `${text?.slice?.(0, maxLength) ?? ''}...` : text
  }

  if (loading || loadingStats) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Sparkles className="h-8 w-8 animate-spin mx-auto text-blue-500" />
          <p className="text-muted-foreground">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    window?.location?.replace?.('/')
    return null
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen flex flex-col">
        <Header />
      
      <main className="flex-1 py-8">
        <div className="container max-w-6xl space-y-8">
          {/* Header */}
          <div className="space-y-2">
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back! Here's an overview of your caption generation activity.
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Usage Remaining</CardTitle>
                <Sparkles className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {usageInfo?.subscriptionStatus === 'active' ? '∞' : usageInfo?.trialUsesRemaining}
                </div>
                <p className="text-xs text-muted-foreground">
                  {usageInfo?.subscriptionStatus === 'active' ? 'Unlimited' : 'Trial uses left'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Generated</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.totalCaptionsGenerated}</div>
                <p className="text-xs text-muted-foreground">
                  Caption sets created
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">This Month</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.thisMonthGenerations}</div>
                <p className="text-xs text-muted-foreground">
                  Generated this month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Saved Captions</CardTitle>
                <History className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.savedCaptions}</div>
                <p className="text-xs text-muted-foreground">
                  Saved to history
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="flex gap-4">
            <Button asChild size="lg">
              <a href="/" className="flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Generate New Captions
              </a>
            </Button>

            {usageInfo?.subscriptionStatus === 'trial' && usageInfo?.trialUsesRemaining === 0 && (
              <Button variant="outline" size="lg">
                Upgrade to Pro - $20/month
              </Button>
            )}
          </div>

          {/* Recent Caption History */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Caption Sets</CardTitle>
              <CardDescription>
                Your recently generated caption sets
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentCaptions?.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No captions generated yet</p>
                  <p className="text-sm">Create your first caption set to see it here</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentCaptions?.map?.((caption) => (
                    <div
                      key={caption?.id}
                      className="flex items-start justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <span>{formatDate(caption?.created_at)}</span>
                          <span>•</span>
                          <span className="capitalize">{caption?.content_type?.replace?.('_', ' ')}</span>
                          <span>•</span>
                          <span className="capitalize">{caption?.target_tone}</span>
                          {caption?.is_saved && (
                            <>
                              <span>•</span>
                              <span className="text-green-600">Saved</span>
                            </>
                          )}
                        </div>
                        
                        <div className="text-sm">
                          <p className="line-clamp-2">
                            {truncateText(caption?.original_content ?? '', 150)}
                          </p>
                        </div>

                        <div className="flex gap-2 text-xs">
                          <span className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                            Twitter: {caption?.twitter_char_count} chars
                          </span>
                          <span className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                            LinkedIn: {caption?.linkedin_char_count} chars
                          </span>
                          <span className="bg-pink-100 dark:bg-pink-900 text-pink-700 dark:text-pink-300 px-2 py-1 rounded">
                            Instagram: {caption?.instagram_char_count} chars
                          </span>
                          <span className="bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 px-2 py-1 rounded">
                            TikTok: {caption?.tiktok_char_count} chars
                          </span>
                        </div>
                      </div>

                      <div className="ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteCaption(caption?.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>

        <Footer />
      </div>
    </ProtectedRoute>
  )
}
