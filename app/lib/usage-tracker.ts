
import { supabase } from './supabase'

export async function checkUsageLimit(userId: string): Promise<{
  canGenerate: boolean
  trialUsesRemaining: number
  subscriptionStatus: string
}> {
  try {
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('subscription_status, trial_uses_remaining, subscription_end_date')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Error checking usage limit:', error)
      return { canGenerate: false, trialUsesRemaining: 0, subscriptionStatus: 'error' }
    }

    // Check if user has active subscription
    if (profile?.subscription_status === 'active') {
      const now = new Date()
      const endDate = profile?.subscription_end_date ? new Date(profile.subscription_end_date) : null
      
      if (endDate && now < endDate) {
        return { canGenerate: true, trialUsesRemaining: 0, subscriptionStatus: 'active' }
      } else {
        // Subscription expired
        await supabase
          .from('profiles')
          .update({ subscription_status: 'expired' })
          .eq('id', userId)
        return { canGenerate: false, trialUsesRemaining: 0, subscriptionStatus: 'expired' }
      }
    }

    // Check trial uses
    const trialUsesRemaining = profile?.trial_uses_remaining ?? 0
    const canGenerate = trialUsesRemaining > 0

    return {
      canGenerate,
      trialUsesRemaining,
      subscriptionStatus: profile?.subscription_status ?? 'trial'
    }
  } catch (error) {
    console.error('Error checking usage limit:', error)
    return { canGenerate: false, trialUsesRemaining: 0, subscriptionStatus: 'error' }
  }
}

export async function trackUsage(
  userId: string, 
  actionType: 'generation' | 'regeneration' | 'save' | 'copy',
  captionGenerationId?: string,
  platform?: 'twitter' | 'linkedin' | 'instagram' | 'tiktok'
): Promise<void> {
  try {
    // Log the action
    await supabase
      .from('usage_logs')
      .insert({
        user_id: userId,
        action_type: actionType,
        caption_generation_id: captionGenerationId,
        platform: platform
      })

    // If it's a generation, decrement trial uses
    if (actionType === 'generation') {
      const { data: profile } = await supabase
        .from('profiles')
        .select('trial_uses_remaining, subscription_status')
        .eq('id', userId)
        .single()

      if (profile?.subscription_status === 'trial' && (profile?.trial_uses_remaining ?? 0) > 0) {
        await supabase
          .from('profiles')
          .update({ 
            trial_uses_remaining: (profile.trial_uses_remaining ?? 0) - 1,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId)
      }
    }
  } catch (error) {
    console.error('Error tracking usage:', error)
  }
}

export async function getUserStats(userId: string): Promise<{
  totalCaptionsGenerated: number
  thisMonthGenerations: number
  savedCaptions: number
}> {
  try {
    const thisMonth = new Date()
    thisMonth.setDate(1)
    thisMonth.setHours(0, 0, 0, 0)

    // Get total captions generated
    const { count: totalCount } = await supabase
      .from('caption_generations')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    // Get this month's generations
    const { count: thisMonthCount } = await supabase
      .from('caption_generations')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .gte('created_at', thisMonth.toISOString())

    // Get saved captions
    const { count: savedCount } = await supabase
      .from('caption_generations')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('is_saved', true)

    return {
      totalCaptionsGenerated: totalCount ?? 0,
      thisMonthGenerations: thisMonthCount ?? 0,
      savedCaptions: savedCount ?? 0
    }
  } catch (error) {
    console.error('Error getting user stats:', error)
    return {
      totalCaptionsGenerated: 0,
      thisMonthGenerations: 0,
      savedCaptions: 0
    }
  }
}
