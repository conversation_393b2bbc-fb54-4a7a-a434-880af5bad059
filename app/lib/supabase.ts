
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pelwkdlfisqywhwaakha.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBlbHdrZGxmaXNxeXdod2Fha2hhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTgwNzg0NDYsImV4cCI6MjA3MzY1NDQ0Nn0.ut6OskHjPPJJSH0B4U-Ar8sdRFhWiZA7sHhZ0_JOdwQ'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Profile = {
  id: string
  email: string
  full_name?: string
  subscription_status: 'trial' | 'active' | 'cancelled' | 'expired'
  subscription_plan: 'free' | 'pro'
  trial_uses_remaining: number
  subscription_end_date?: string
  created_at: string
  updated_at: string
}

export type CaptionGeneration = {
  id: string
  user_id: string
  original_content: string
  content_type: 'blog_post' | 'article' | 'social_post' | 'other'
  target_tone: 'professional' | 'casual' | 'humorous' | 'inspirational' | 'educational'
  twitter_caption?: string
  twitter_char_count?: number
  linkedin_caption?: string
  linkedin_char_count?: number
  instagram_caption?: string
  instagram_char_count?: number
  tiktok_caption?: string
  tiktok_char_count?: number
  generation_time_ms?: number
  is_saved: boolean
  created_at: string
}

export type UsageLog = {
  id: string
  user_id: string
  action_type: 'generation' | 'regeneration' | 'save' | 'copy'
  caption_generation_id?: string
  platform?: 'twitter' | 'linkedin' | 'instagram' | 'tiktok'
  created_at: string
}
