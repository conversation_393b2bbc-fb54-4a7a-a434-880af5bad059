
interface PlatformSpecs {
  maxLength: number
  optimalLength: [number, number]
  hashtagLimit: number
  style: 'professional' | 'casual' | 'creative' | 'seo-focused'
  tone: string
}

const PLATFORM_SPECS: Record<string, PlatformSpecs> = {
  twitter: {
    maxLength: 280,
    optimalLength: [71, 100],
    hashtagLimit: 2,
    style: 'casual',
    tone: 'conversational and immediate, real-time oriented, direct and concise'
  },
  linkedin: {
    maxLength: 3000,
    optimalLength: [150, 300],
    hashtagLimit: 5,
    style: 'professional',
    tone: 'professional and authoritative, educational and value-driven, thought leadership focused'
  },
  instagram: {
    maxLength: 2200,
    optimalLength: [100, 150],
    hashtagLimit: 15,
    style: 'creative',
    tone: 'visual storytelling focused, authentic and personal, creative and inspirational'
  },
  tiktok: {
    maxLength: 300,
    optimalLength: [80, 100],
    hashtagLimit: 5,
    style: 'seo-focused',
    tone: 'casual and conversational, trend-aware and current, entertainment-focused'
  }
}

export interface CaptionSet {
  id?: string
  twitter: {
    caption: string
    charCount: number
  }
  linkedin: {
    caption: string
    charCount: number
  }
  instagram: {
    caption: string
    charCount: number
  }
  tiktok: {
    caption: string
    charCount: number
  }
  generationTime: number
}

export async function generateCaptions(
  originalContent: string,
  contentType: string,
  targetTone: string
): Promise<CaptionSet> {
  const startTime = Date.now()
  
  const prompts = {
    twitter: `Transform this long-form content into an optimized Twitter/X post:

Original content: "${originalContent}"

Requirements:
- Character limit: ${PLATFORM_SPECS.twitter.optimalLength[0]}-${PLATFORM_SPECS.twitter.optimalLength[1]} characters for optimal engagement
- Maximum: ${PLATFORM_SPECS.twitter.maxLength} characters
- Include maximum ${PLATFORM_SPECS.twitter.hashtagLimit} relevant hashtags
- Tone: ${PLATFORM_SPECS.twitter.tone}
- Target tone: ${targetTone}
- Front-load key information
- Include a call-to-action or conversation starter
- Make it engaging and shareable

Return only the caption text, no explanations.`,

    linkedin: `Transform this long-form content into an optimized LinkedIn post:

Original content: "${originalContent}"

Requirements:
- Character limit: ${PLATFORM_SPECS.linkedin.optimalLength[0]}-${PLATFORM_SPECS.linkedin.optimalLength[1]} characters for optimal engagement
- Maximum: ${PLATFORM_SPECS.linkedin.maxLength} characters
- Include ${PLATFORM_SPECS.linkedin.hashtagLimit} professional hashtags at the end
- Tone: ${PLATFORM_SPECS.linkedin.tone}
- Target tone: ${targetTone}
- Add professional insights and value
- Include a thought-provoking question at the end
- Format with line breaks for readability
- Focus on business value and expertise

Return only the caption text, no explanations.`,

    instagram: `Transform this long-form content into an optimized Instagram post:

Original content: "${originalContent}"

Requirements:
- Character limit: ${PLATFORM_SPECS.instagram.optimalLength[0]}-${PLATFORM_SPECS.instagram.optimalLength[1]} characters for optimal engagement
- Maximum: ${PLATFORM_SPECS.instagram.maxLength} characters
- Include ${PLATFORM_SPECS.instagram.hashtagLimit} relevant hashtags
- Tone: ${PLATFORM_SPECS.instagram.tone}
- Target tone: ${targetTone}
- Add visual storytelling elements
- Use emojis strategically for visual appeal
- Include community engagement hooks
- Format with line breaks and visual structure

Return only the caption text, no explanations.`,

    tiktok: `Transform this long-form content into an optimized TikTok caption:

Original content: "${originalContent}"

Requirements:
- Character limit: ${PLATFORM_SPECS.tiktok.optimalLength[0]}-${PLATFORM_SPECS.tiktok.optimalLength[1]} characters for optimal engagement
- Maximum: ${PLATFORM_SPECS.tiktok.maxLength} characters
- Include ${PLATFORM_SPECS.tiktok.hashtagLimit} trending/niche hashtags
- Tone: ${PLATFORM_SPECS.tiktok.tone}
- Target tone: ${targetTone}
- Front-load key message in first line
- Include SEO-friendly keywords naturally
- Make it trend-aware and engaging
- Add entertainment value

Return only the caption text, no explanations.`
  }

  const results: CaptionSet = {
    twitter: { caption: '', charCount: 0 },
    linkedin: { caption: '', charCount: 0 },
    instagram: { caption: '', charCount: 0 },
    tiktok: { caption: '', charCount: 0 },
    generationTime: 0
  }

  // Generate captions for each platform
  for (const platform of ['twitter', 'linkedin', 'instagram', 'tiktok']) {
    try {
      const response = await fetch('https://apps.abacus.ai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.ABACUSAI_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gpt-4.1-mini',
          messages: [{ 
            role: 'user', 
            content: prompts[platform as keyof typeof prompts] 
          }],
          max_tokens: 500,
          temperature: 0.7
        })
      })

      if (!response?.ok) {
        throw new Error(`HTTP error! status: ${response?.status}`)
      }

      const data = await response?.json?.()
      const caption = data?.choices?.[0]?.message?.content?.trim?.() ?? ''
      
      if (platform === 'twitter') {
        results.twitter = { caption, charCount: caption?.length ?? 0 }
      } else if (platform === 'linkedin') {
        results.linkedin = { caption, charCount: caption?.length ?? 0 }
      } else if (platform === 'instagram') {
        results.instagram = { caption, charCount: caption?.length ?? 0 }
      } else if (platform === 'tiktok') {
        results.tiktok = { caption, charCount: caption?.length ?? 0 }
      }
    } catch (error) {
      console.error(`Error generating ${platform} caption:`, error)
      const errorCaption = `Error generating ${platform} caption. Please try again.`
      
      if (platform === 'twitter') {
        results.twitter = { caption: errorCaption, charCount: 0 }
      } else if (platform === 'linkedin') {
        results.linkedin = { caption: errorCaption, charCount: 0 }
      } else if (platform === 'instagram') {
        results.instagram = { caption: errorCaption, charCount: 0 }
      } else if (platform === 'tiktok') {
        results.tiktok = { caption: errorCaption, charCount: 0 }
      }
    }
  }

  results.generationTime = Date.now() - startTime
  return results
}
